"""
PDF Engine Core

Central abstraction layer that coordinates all PDF generation operations.
Provides a unified interface while maintaining clean separation of concerns.
Enhanced for professional document generation with precise layout control.
"""

from typing import Optional, Dict, Any, Union, Tuple
from pathlib import Path
import io

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.units import mm, cm, inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from .exceptions import PDFEngineError, ConfigurationError
from .document import DocumentBuilder
from .forms import FormBuilder
from .layout import LayoutManager


class PDFEngine:
    """
    Core PDF generation engine with modular architecture.

    Provides unified interface for creating static and interactive PDFs
    while maintaining clean abstraction boundaries and extensibility.
    Enhanced for professional Norwegian employment contract generation.
    """

    # Default configuration for Norwegian employment contracts
    DEFAULT_CONFIG = {
        'page_size': A4,
        'margins': {
            'top': 2*cm,
            'bottom': 2*cm,
            'left': 44,  # 44 points as per reference document
            'right': 44
        },
        'fonts': {
            'primary': 'Calibri',
            'fallback': 'Helvetica'
        },
        'font_sizes': {
            'title': 16,
            'heading': 9,
            'body': 9,
            'label': 8
        },
        'language': 'nb',  # Norwegian Bokmål
        'document_type': 'employment_contract'
    }

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize PDF engine with optional configuration.

        Args:
            config: Optional configuration dictionary
        """
        # Merge with default configuration
        self.config = {**self.DEFAULT_CONFIG}
        if config:
            self.config.update(config)

        self._validate_config()
        self._setup_fonts()

        # Initialize core modules with enhanced config
        self.document = DocumentBuilder(self.config)
        self.forms = FormBuilder(self.config)
        self.layout = LayoutManager(self.config)
        
    def _validate_config(self) -> None:
        """Validate engine configuration."""
        if not isinstance(self.config, dict):
            raise ConfigurationError("Configuration must be a dictionary")

        # Validate required configuration keys
        required_keys = ['page_size', 'margins', 'fonts', 'font_sizes']
        for key in required_keys:
            if key not in self.config:
                raise ConfigurationError(f"Missing required configuration key: {key}")

    def _setup_fonts(self) -> None:
        """Setup fonts for professional document generation."""
        try:
            # Try to register Calibri font if available
            # Note: In production, you would need to provide Calibri font files
            # For now, we'll use Helvetica as fallback
            primary_font = self.config['fonts']['primary']
            fallback_font = self.config['fonts']['fallback']

            # Check if primary font is available
            try:
                # This would register Calibri if font files were available
                # TTFont('Calibri', 'path/to/calibri.ttf')
                # TTFont('Calibri-Bold', 'path/to/calibri-bold.ttf')
                # pdfmetrics.registerFont(TTFont('Calibri', 'path/to/calibri.ttf'))
                # pdfmetrics.registerFont(TTFont('Calibri-Bold', 'path/to/calibri-bold.ttf'))
                pass
            except Exception:
                # Fall back to Helvetica
                self.config['fonts']['primary'] = fallback_font

        except Exception as e:
            raise ConfigurationError(f"Font setup failed: {e}")

    def get_font_name(self, weight: str = 'normal') -> str:
        """
        Get the appropriate font name for the given weight.

        Args:
            weight: Font weight ('normal', 'bold')

        Returns:
            Font name string
        """
        primary_font = self.config['fonts']['primary']
        if weight == 'bold':
            return f"{primary_font}-Bold"
        return primary_font

    def get_font_size(self, style: str) -> int:
        """
        Get font size for the given style.

        Args:
            style: Style name ('title', 'heading', 'body', 'label')

        Returns:
            Font size in points
        """
        return self.config['font_sizes'].get(style, 9)
    
    def create_document(self,
                       output_path: Optional[Union[str, Path]] = None,
                       **kwargs) -> 'DocumentBuilder':
        """
        Create a new document builder instance.

        Args:
            output_path: Optional output file path
            **kwargs: Additional document options

        Returns:
            DocumentBuilder instance
        """
        return self.document.new_document(output_path, **kwargs)

    def create_norwegian_contract(self,
                                 output_path: Optional[Union[str, Path]] = None,
                                 **kwargs) -> 'DocumentBuilder':
        """
        Create a Norwegian employment contract document.

        Args:
            output_path: Optional output file path
            **kwargs: Additional contract options

        Returns:
            DocumentBuilder instance configured for Norwegian contracts
        """
        # Set Norwegian-specific configuration
        contract_config = {
            'document_type': 'norwegian_employment_contract',
            'language': 'nb',
            'legal_compliance': True,
            **kwargs
        }

        return self.document.new_document(output_path, **contract_config)
    
    def create_form(self,
                   output_path: Optional[Union[str, Path]] = None,
                   **kwargs) -> 'FormBuilder':
        """
        Create a new interactive form builder instance.

        Args:
            output_path: Optional output file path
            **kwargs: Additional form options

        Returns:
            FormBuilder instance
        """
        return self.forms.new_form(output_path, **kwargs)

    def create_norwegian_contract_form(self,
                                      output_path: Optional[Union[str, Path]] = None,
                                      **kwargs) -> 'FormBuilder':
        """
        Create a Norwegian employment contract interactive form.

        Args:
            output_path: Optional output file path
            **kwargs: Additional form options

        Returns:
            FormBuilder instance configured for Norwegian contract forms
        """
        # Set Norwegian contract form configuration
        form_config = {
            'form_type': 'norwegian_employment_contract',
            'language': 'nb',
            'field_count': 47,  # As per reference document
            'legal_compliance': True,
            'professional_styling': True,
            **kwargs
        }

        return self.forms.new_form(output_path, **form_config)
    
    def get_layout_manager(self) -> 'LayoutManager':
        """
        Get the layout manager for advanced layout operations.
        
        Returns:
            LayoutManager instance
        """
        return self.layout
    
    def render_to_bytes(self, builder) -> bytes:
        """
        Render a document/form builder to bytes.
        
        Args:
            builder: DocumentBuilder or FormBuilder instance
            
        Returns:
            PDF content as bytes
        """
        try:
            return builder.render_to_bytes()
        except Exception as e:
            raise PDFEngineError(f"Failed to render PDF: {e}") from e
    
    def render_to_file(self, builder, output_path: Union[str, Path]) -> None:
        """
        Render a document/form builder to file.

        Args:
            builder: DocumentBuilder or FormBuilder instance
            output_path: Output file path
        """
        try:
            builder.render_to_file(output_path)
        except Exception as e:
            raise PDFEngineError(f"Failed to save PDF to {output_path}: {e}") from e

    def get_page_dimensions(self) -> Tuple[float, float]:
        """
        Get page dimensions in points.

        Returns:
            Tuple of (width, height) in points
        """
        return self.config['page_size']

    def get_margins(self) -> Dict[str, float]:
        """
        Get page margins.

        Returns:
            Dictionary with margin values in points
        """
        return self.config['margins'].copy()

    def convert_units(self, value: float, from_unit: str, to_unit: str = 'points') -> float:
        """
        Convert between different units.

        Args:
            value: Value to convert
            from_unit: Source unit ('mm', 'cm', 'inch', 'points')
            to_unit: Target unit ('mm', 'cm', 'inch', 'points')

        Returns:
            Converted value
        """
        # Convert to points first
        if from_unit == 'mm':
            points = value * mm
        elif from_unit == 'cm':
            points = value * cm
        elif from_unit == 'inch':
            points = value * inch
        else:  # points
            points = value

        # Convert from points to target unit
        if to_unit == 'mm':
            return points / mm
        elif to_unit == 'cm':
            return points / cm
        elif to_unit == 'inch':
            return points / inch
        else:  # points
            return points

    def validate_norwegian_contract_compliance(self, builder) -> Dict[str, Any]:
        """
        Validate that a document/form meets Norwegian employment contract requirements.

        Args:
            builder: DocumentBuilder or FormBuilder instance

        Returns:
            Validation results dictionary
        """
        validation_results = {
            'compliant': True,
            'issues': [],
            'warnings': [],
            'required_sections': [
                'Arbeidsgiver/virksomhet',
                'Arbeidstaker',
                'Arbeidsplass',
                'Ansatt som',
                'Arbeidsforholdets varighet og arbeidstid',
                'Eventuell prøvetid',
                'Lønn',
                'Tariffavtale',
                'Eventuell rett til kompetanseutvikling',
                'Ytelser til sosial trygghet',
                'Innleiers identitet',
                'Andre opplysninger',
                'Underskrifter'
            ]
        }

        # This would be implemented to check actual document content
        # For now, return basic structure
        return validation_results
