#!/usr/bin/env python3
"""
Comprehensive Alignment Verification Tool for Norwegian Employment Contract
===========================================================================

This tool creates a specialized PDF designed to verify and demonstrate proper
alignment techniques used in the Norwegian employment contract generation.

Features:
- Visual alignment guides and measurements
- Before/after comparison sections
- Coordinate system demonstration
- Field type verification (single, split, multi-line)
- Interactive form testing
- Professional typography verification

Author: PDF Generation Research Team
Date: 2024
"""

import sys
import os
from pathlib import Path
from typing import Tuple, List, Dict, Any

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import black, grey, red, blue, green, lightgrey
from reportlab.lib.units import mm, cm

class AlignmentVerificationTool:
    """
    Comprehensive tool for verifying PDF alignment and form field positioning.
    """
    
    def __init__(self):
        """Initialize the verification tool with standard settings."""
        self.page_width, self.page_height = A4
        
        # Standard measurements from research
        self.measurements = {
            'left_margin': 44,
            'right_margin': 44,
            'top_margin': 92,
            'bottom_margin': 57,
            'field_height': 15,
            'label_offset': 14,
            'section_gap': 30,
            'field_gap': 28,
            'field_width_full': 505.3,
            'field_width_split_1': 364.8,
            'field_width_split_2': 137.1,
            'split_gap': 5.4
        }
        
        # Typography settings
        self.fonts = {
            'title': ('Helvetica-Bold', 18),
            'section': ('Helvetica-Bold', 12),
            'subsection': ('Helvetica-Bold', 9),
            'label': ('Helvetica', 8),
            'field': ('Helvetica', 9),
            'body': ('Helvetica', 8),
            'caption': ('Helvetica', 7)
        }
        
        # Color scheme for verification
        self.colors = {
            'text': black,
            'field_border': black,
            'guide_line': blue,
            'measurement': red,
            'good_alignment': green,
            'poor_alignment': red,
            'background': lightgrey
        }
    
    def create_verification_pdf(self, output_path: str = "output/alignment_verification.pdf") -> str:
        """
        Create the comprehensive alignment verification PDF.
        
        Args:
            output_path: Path where the PDF will be saved
            
        Returns:
            Path to the generated PDF
        """
        print("Creating comprehensive alignment verification PDF...")
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create canvas
        c = canvas.Canvas(output_path, pagesize=A4)
        
        # Page 1: Overview and Coordinate System
        self._create_overview_page(c)
        c.showPage()
        
        # Page 2: Field Type Demonstrations
        self._create_field_types_page(c)
        c.showPage()
        
        # Page 3: Before/After Comparison
        self._create_comparison_page(c)
        c.showPage()
        
        # Page 4: Interactive Form Testing
        self._create_interactive_testing_page(c)
        
        c.save()
        print(f"Alignment verification PDF generated: {output_path}")
        return output_path
    
    def _create_overview_page(self, c: canvas.Canvas) -> None:
        """Create the overview page with coordinate system explanation."""
        
        # Page title
        font_name, font_size = self.fonts['title']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'], 
                    self.page_height - 50, 
                    "PDF Alignment Verification Tool")
        
        # Subtitle
        font_name, font_size = self.fonts['section']
        c.setFont(font_name, font_size)
        c.drawString(self.measurements['left_margin'], 
                    self.page_height - 80, 
                    "Norwegian Employment Contract - Alignment Analysis")
        
        current_y = self.page_height - 120
        
        # Section 1: Coordinate System Explanation
        current_y = self._add_section_header(c, "1. ReportLab Coordinate System", current_y)
        
        explanations = [
            "• Origin (0,0) is at bottom-left corner of page",
            "• X increases rightward, Y increases upward",
            "• Text drawString() positions text at baseline",
            "• Form fields position at bottom-left corner of field area",
            "• Visual alignment requires Y coordinate adjustments"
        ]
        
        current_y = self._add_text_list(c, explanations, current_y)
        
        # Visual coordinate demonstration
        current_y -= 30
        self._draw_coordinate_demonstration(c, current_y)
        
        current_y -= 100
        
        # Section 2: Alignment Solution
        current_y = self._add_section_header(c, "2. Alignment Solution", current_y)
        
        solution_points = [
            "• Label Y position = Field Y position + 14 points",
            "• This positions label above field with proper visual spacing",
            "• Consistent spacing: 28pt between fields, 30pt between sections",
            "• Professional typography with appropriate font sizes"
        ]
        
        current_y = self._add_text_list(c, solution_points, current_y)
        
        # Section 3: Measurements Reference
        current_y -= 30
        current_y = self._add_section_header(c, "3. Standard Measurements", current_y)
        
        self._draw_measurements_table(c, current_y)
    
    def _add_section_header(self, c: canvas.Canvas, text: str, y_position: float) -> float:
        """Add a section header and return new Y position."""
        font_name, font_size = self.fonts['subsection']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'], y_position, text)
        return y_position - 25
    
    def _add_text_list(self, c: canvas.Canvas, text_list: List[str], y_position: float) -> float:
        """Add a list of text items and return new Y position."""
        font_name, font_size = self.fonts['body']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        
        for text in text_list:
            c.drawString(self.measurements['left_margin'] + 10, y_position, text)
            y_position -= 15
        
        return y_position
    
    def _draw_coordinate_demonstration(self, c: canvas.Canvas, y_position: float) -> None:
        """Draw a visual demonstration of the coordinate system."""
        
        # Draw coordinate axes
        origin_x = self.measurements['left_margin'] + 100
        origin_y = y_position - 50
        
        # X axis
        c.setStrokeColor(self.colors['guide_line'])
        c.setLineWidth(1)
        c.line(origin_x, origin_y, origin_x + 100, origin_y)
        c.drawString(origin_x + 105, origin_y - 5, "X →")
        
        # Y axis  
        c.line(origin_x, origin_y, origin_x, origin_y + 60)
        c.drawString(origin_x - 10, origin_y + 65, "Y ↑")
        
        # Origin label
        c.setFont(*self.fonts['caption'])
        c.drawString(origin_x - 15, origin_y - 15, "(0,0)")
        
        # Demonstration text and field
        demo_y = origin_y + 30
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['text'])
        c.drawString(origin_x + 20, demo_y + 14, "Label (Y + 14)")
        
        # Demo field rectangle
        c.setStrokeColor(self.colors['field_border'])
        c.setFillColor(lightgrey)
        c.rect(origin_x + 20, demo_y, 80, 15, fill=1, stroke=1)
        c.setFillColor(self.colors['text'])
        c.drawString(origin_x + 25, demo_y + 4, "Field (Y)")
    
    def _draw_measurements_table(self, c: canvas.Canvas, y_position: float) -> None:
        """Draw a table of standard measurements."""
        
        table_data = [
            ("Measurement", "Value", "Purpose"),
            ("Left Margin", "44pt", "Standard document margin"),
            ("Label Offset", "14pt", "Label above field spacing"),
            ("Field Height", "15pt", "Standard form field height"),
            ("Field Gap", "28pt", "Spacing between fields"),
            ("Section Gap", "30pt", "Spacing between sections"),
            ("Full Field Width", "505.3pt", "Single field width"),
            ("Split Field 1", "364.8pt", "First part of split field"),
            ("Split Field 2", "137.1pt", "Second part of split field")
        ]
        
        x_start = self.measurements['left_margin']
        col_widths = [120, 80, 200]
        row_height = 15
        
        for i, row in enumerate(table_data):
            y = y_position - (i * row_height)
            
            # Header row styling
            if i == 0:
                c.setFont(*self.fonts['subsection'])
                c.setFillColor(self.colors['text'])
            else:
                c.setFont(*self.fonts['body'])
                c.setFillColor(self.colors['text'])
            
            # Draw row
            x = x_start
            for j, cell in enumerate(row):
                c.drawString(x, y, cell)
                x += col_widths[j]
            
            # Draw separator line after header
            if i == 0:
                c.setStrokeColor(self.colors['text'])
                c.setLineWidth(0.5)
                c.line(x_start, y - 5, x_start + sum(col_widths), y - 5)

    def _create_field_types_page(self, c: canvas.Canvas) -> None:
        """Create page demonstrating different field types with alignment guides."""

        # Page title
        font_name, font_size = self.fonts['title']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'],
                    self.page_height - 50,
                    "Field Type Alignment Verification")

        current_y = self.page_height - 100

        # Single field demonstration
        current_y = self._demonstrate_single_field(c, current_y)

        # Split field demonstration
        current_y -= 50
        current_y = self._demonstrate_split_fields(c, current_y)

        # Multi-line field demonstration
        current_y -= 50
        current_y = self._demonstrate_multiline_field(c, current_y)

    def _demonstrate_single_field(self, c: canvas.Canvas, y_position: float) -> float:
        """Demonstrate single field alignment with visual guides."""

        # Section header
        y_position = self._add_section_header(c, "Single Field Alignment", y_position)

        # Field setup
        field_y = y_position - 40
        label_y = field_y + self.measurements['label_offset']
        x_pos = self.measurements['left_margin']

        # Draw alignment guides
        self._draw_alignment_guides(c, x_pos, field_y, label_y,
                                  self.measurements['field_width_full'])

        # Add label
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['text'])
        c.drawString(x_pos, label_y, "Example Label:")

        # Add form field
        c.acroForm.textfield(
            name="single_field_demo",
            x=x_pos,
            y=field_y,
            width=self.measurements['field_width_full'],
            height=self.measurements['field_height'],
            value="Example field content",
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add underline
        c.setStrokeColor(self.colors['field_border'])
        c.setLineWidth(0.5)
        c.line(x_pos, field_y, x_pos + self.measurements['field_width_full'], field_y)

        # Add measurements annotations
        self._add_measurement_annotations(c, x_pos, field_y, label_y)

        return field_y - 30

    def _demonstrate_split_fields(self, c: canvas.Canvas, y_position: float) -> float:
        """Demonstrate split field alignment with visual guides."""

        # Section header
        y_position = self._add_section_header(c, "Split Field Alignment", y_position)

        # Field setup
        field_y = y_position - 40
        label_y = field_y + self.measurements['label_offset']
        x1 = self.measurements['left_margin']
        x2 = x1 + self.measurements['field_width_split_1'] + self.measurements['split_gap']

        # Draw alignment guides for both fields
        self._draw_alignment_guides(c, x1, field_y, label_y,
                                  self.measurements['field_width_split_1'])
        self._draw_alignment_guides(c, x2, field_y, label_y,
                                  self.measurements['field_width_split_2'])

        # Add labels
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['text'])
        c.drawString(x1, label_y, "First Field:")
        c.drawString(x2, label_y, "Second:")

        # Add form fields
        c.acroForm.textfield(
            name="split_field_1_demo",
            x=x1,
            y=field_y,
            width=self.measurements['field_width_split_1'],
            height=self.measurements['field_height'],
            value="First field content",
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        c.acroForm.textfield(
            name="split_field_2_demo",
            x=x2,
            y=field_y,
            width=self.measurements['field_width_split_2'],
            height=self.measurements['field_height'],
            value="Second",
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add underlines
        c.setStrokeColor(self.colors['field_border'])
        c.setLineWidth(0.5)
        c.line(x1, field_y, x1 + self.measurements['field_width_split_1'], field_y)
        c.line(x2, field_y, x2 + self.measurements['field_width_split_2'], field_y)

        # Add gap measurement
        gap_center = x1 + self.measurements['field_width_split_1'] + (self.measurements['split_gap'] / 2)
        c.setFont(*self.fonts['caption'])
        c.setFillColor(self.colors['measurement'])
        c.drawString(gap_center - 10, field_y - 10, f"{self.measurements['split_gap']}pt gap")

        return field_y - 30

    def _demonstrate_multiline_field(self, c: canvas.Canvas, y_position: float) -> float:
        """Demonstrate multi-line field alignment with visual guides."""

        # Section header
        y_position = self._add_section_header(c, "Multi-line Field Alignment", y_position)

        # Field setup
        field_height = 36
        field_y = y_position - 50
        label_y = field_y + field_height + 8
        x_pos = self.measurements['left_margin']

        # Draw alignment guides
        self._draw_alignment_guides(c, x_pos, field_y, label_y,
                                  self.measurements['field_width_full'], field_height)

        # Add label
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['text'])
        c.drawString(x_pos, label_y, "Multi-line Description:")

        # Add form field
        c.acroForm.textfield(
            name="multiline_field_demo",
            x=x_pos,
            y=field_y,
            width=self.measurements['field_width_full'],
            height=field_height,
            value="Line 1 of multi-line content\nLine 2 of content",
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add multiple underlines
        c.setStrokeColor(self.colors['field_border'])
        c.setLineWidth(0.5)
        c.line(x_pos, field_y + 18, x_pos + self.measurements['field_width_full'], field_y + 18)
        c.line(x_pos, field_y, x_pos + self.measurements['field_width_full'], field_y)

        return field_y - 30

    def _draw_alignment_guides(self, c: canvas.Canvas, x: float, field_y: float,
                              label_y: float, width: float, height: float = None) -> None:
        """Draw visual alignment guides for a field."""

        if height is None:
            height = self.measurements['field_height']

        # Draw guide lines
        c.setStrokeColor(self.colors['guide_line'])
        c.setLineWidth(0.5)
        c.setDash([2, 2])

        # Vertical guides at field edges
        c.line(x - 5, field_y - 5, x - 5, label_y + 10)
        c.line(x + width + 5, field_y - 5, x + width + 5, label_y + 10)

        # Horizontal guides for alignment
        c.line(x - 10, field_y, x + width + 10, field_y)
        c.line(x - 10, label_y, x + width + 10, label_y)

        # Reset line style
        c.setDash([])

    def _add_measurement_annotations(self, c: canvas.Canvas, x: float,
                                   field_y: float, label_y: float) -> None:
        """Add measurement annotations to show spacing."""

        # Vertical measurement line
        measure_x = x + self.measurements['field_width_full'] + 20
        c.setStrokeColor(self.colors['measurement'])
        c.setLineWidth(1)
        c.line(measure_x, field_y, measure_x, label_y)

        # Measurement arrows
        arrow_size = 3
        c.line(measure_x - arrow_size, field_y + arrow_size, measure_x, field_y)
        c.line(measure_x + arrow_size, field_y + arrow_size, measure_x, field_y)
        c.line(measure_x - arrow_size, label_y - arrow_size, measure_x, label_y)
        c.line(measure_x + arrow_size, label_y - arrow_size, measure_x, label_y)

        # Measurement text
        c.setFont(*self.fonts['caption'])
        c.setFillColor(self.colors['measurement'])
        mid_y = field_y + (label_y - field_y) / 2
        c.drawString(measure_x + 5, mid_y, f"{self.measurements['label_offset']}pt")

    def _create_comparison_page(self, c: canvas.Canvas) -> None:
        """Create before/after comparison page."""

        # Page title
        font_name, font_size = self.fonts['title']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'],
                    self.page_height - 50,
                    "Before/After Alignment Comparison")

        current_y = self.page_height - 100

        # Before section (poor alignment)
        current_y = self._add_section_header(c, "BEFORE: Poor Alignment", current_y)
        current_y = self._demonstrate_poor_alignment(c, current_y)

        current_y -= 50

        # After section (good alignment)
        current_y = self._add_section_header(c, "AFTER: Proper Alignment", current_y)
        current_y = self._demonstrate_good_alignment(c, current_y)

        # Add improvement summary
        current_y -= 50
        self._add_improvement_summary(c, current_y)

    def _demonstrate_poor_alignment(self, c: canvas.Canvas, y_position: float) -> float:
        """Demonstrate poor alignment (labels and fields at same Y)."""

        field_y = y_position - 40
        x_pos = self.measurements['left_margin']

        # Poor alignment: label and field at same Y
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['poor_alignment'])
        c.drawString(x_pos, field_y, "Overlapping Label:")

        # Field at same Y position
        c.acroForm.textfield(
            name="poor_alignment_demo",
            x=x_pos,
            y=field_y,
            width=200,
            height=self.measurements['field_height'],
            value="Field content overlaps",
            forceBorder=True,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add problem annotation
        c.setFont(*self.fonts['caption'])
        c.setFillColor(self.colors['poor_alignment'])
        c.drawString(x_pos + 220, field_y, "❌ Label and field overlap!")

        return field_y - 30

    def _demonstrate_good_alignment(self, c: canvas.Canvas, y_position: float) -> float:
        """Demonstrate good alignment (proper label offset)."""

        field_y = y_position - 40
        label_y = field_y + self.measurements['label_offset']
        x_pos = self.measurements['left_margin']

        # Good alignment: label above field
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['good_alignment'])
        c.drawString(x_pos, label_y, "Properly Positioned Label:")

        # Field below label
        c.acroForm.textfield(
            name="good_alignment_demo",
            x=x_pos,
            y=field_y,
            width=200,
            height=self.measurements['field_height'],
            value="Field content is clear",
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add underline
        c.setStrokeColor(self.colors['field_border'])
        c.setLineWidth(0.5)
        c.line(x_pos, field_y, x_pos + 200, field_y)

        # Add success annotation
        c.setFont(*self.fonts['caption'])
        c.setFillColor(self.colors['good_alignment'])
        c.drawString(x_pos + 220, field_y + 7, "✓ Professional alignment!")

        return field_y - 30

    def _add_improvement_summary(self, c: canvas.Canvas, y_position: float) -> None:
        """Add summary of improvements made."""

        y_position = self._add_section_header(c, "Key Improvements", y_position)

        improvements = [
            "✓ Labels positioned 14 points above form fields",
            "✓ No overlapping text and form elements",
            "✓ Consistent spacing throughout document",
            "✓ Professional visual hierarchy",
            "✓ Proper coordinate system handling",
            "✓ Norwegian text rendering support"
        ]

        self._add_text_list(c, improvements, y_position)

    def _create_interactive_testing_page(self, c: canvas.Canvas) -> None:
        """Create interactive form testing page."""

        # Page title
        font_name, font_size = self.fonts['title']
        c.setFont(font_name, font_size)
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'],
                    self.page_height - 50,
                    "Interactive Form Testing")

        current_y = self.page_height - 100

        # Instructions
        current_y = self._add_section_header(c, "Testing Instructions", current_y)

        instructions = [
            "1. Fill out the form fields below to test interactivity",
            "2. Verify that text aligns properly within fields",
            "3. Check that labels are clearly readable above fields",
            "4. Test tab order and field navigation",
            "5. Verify Norwegian character support (æ, ø, å)"
        ]

        current_y = self._add_text_list(c, instructions, current_y)

        # Test form
        current_y -= 30
        current_y = self._add_section_header(c, "Test Form", current_y)

        # Add test fields using the proper alignment
        test_fields = [
            ("test_name", "Navn (Name):", ""),
            ("test_company", "Firma (Company):", ""),
            ("test_address", "Adresse (Address):", ""),
            ("test_norwegian", "Norsk tekst (Norwegian text):", "æøå ÆØÅ")
        ]

        for field_name, label, default_value in test_fields:
            current_y = self._add_test_field(c, field_name, label, default_value, current_y)

        # Add completion note
        current_y -= 30
        c.setFont(*self.fonts['body'])
        c.setFillColor(self.colors['text'])
        c.drawString(self.measurements['left_margin'], current_y,
                    "Form testing complete. All fields should display proper alignment.")

    def _add_test_field(self, c: canvas.Canvas, name: str, label: str,
                       default_value: str, y_position: float) -> float:
        """Add a test field with proper alignment."""

        field_y = y_position - 30
        label_y = field_y + self.measurements['label_offset']
        x_pos = self.measurements['left_margin']

        # Add label
        c.setFont(*self.fonts['label'])
        c.setFillColor(self.colors['text'])
        c.drawString(x_pos, label_y, label)

        # Add form field
        c.acroForm.textfield(
            name=name,
            x=x_pos,
            y=field_y,
            width=300,
            height=self.measurements['field_height'],
            value=default_value,
            forceBorder=False,
            fontName=self.fonts['field'][0],
            fontSize=self.fonts['field'][1]
        )

        # Add underline
        c.setStrokeColor(self.colors['field_border'])
        c.setLineWidth(0.5)
        c.line(x_pos, field_y, x_pos + 300, field_y)

        return field_y - 10


def main():
    """Main function to run the alignment verification tool."""

    print("PDF Alignment Verification Tool")
    print("=" * 60)
    print("Creating comprehensive alignment verification PDF...")
    print("This tool demonstrates proper alignment techniques for")
    print("Norwegian employment contract form generation.")
    print("=" * 60)

    try:
        # Create verification tool
        tool = AlignmentVerificationTool()

        # Generate verification PDF
        output_path = tool.create_verification_pdf()

        print("\n" + "=" * 60)
        print("SUCCESS: Alignment verification PDF generated!")
        print(f"Output: {output_path}")

        # Show file information
        if os.path.exists(output_path):
            size = os.path.getsize(output_path)
            print(f"File size: {size:,} bytes")

        print("\nVerification PDF Contents:")
        print("📄 Page 1: Coordinate System & Measurements")
        print("📄 Page 2: Field Type Demonstrations")
        print("📄 Page 3: Before/After Comparison")
        print("📄 Page 4: Interactive Form Testing")

        print("\nKey Features Verified:")
        print("✓ ReportLab coordinate system understanding")
        print("✓ Proper label-to-field alignment (14pt offset)")
        print("✓ Single, split, and multi-line field support")
        print("✓ Visual alignment guides and measurements")
        print("✓ Before/after comparison demonstrations")
        print("✓ Interactive form field testing")
        print("✓ Norwegian character support (æøå)")

        print("\nNext Steps:")
        print("1. Open the generated PDF to review alignment")
        print("2. Test interactive form fields")
        print("3. Verify visual alignment guides")
        print("4. Compare with reference Norwegian contract")

        return 0

    except Exception as e:
        print(f"\nERROR: Failed to generate verification PDF")
        print(f"Error details: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
