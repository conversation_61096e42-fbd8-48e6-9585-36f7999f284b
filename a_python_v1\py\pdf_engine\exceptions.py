"""
PDF Engine Exceptions

Custom exception hierarchy for the PDF generation system.
Provides clear error handling and debugging capabilities.
"""


class PDFEngineError(Exception):
    """Base exception for all PDF engine errors."""
    pass


class ValidationError(PDFEngineError):
    """Raised when input validation fails."""
    pass


class LayoutError(PDFEngineError):
    """Raised when layout operations fail."""
    pass


class FormError(PDFEngineError):
    """Raised when form operations fail."""
    pass


class RenderError(PDFEngineError):
    """Raised when PDF rendering fails."""
    pass


class ConfigurationError(PDFEngineError):
    """Raised when configuration is invalid."""
    pass
