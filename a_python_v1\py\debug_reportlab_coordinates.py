#!/usr/bin/env python3
"""
Debug ReportLab coordinate system and form field positioning.
Based on research of ReportLab documentation and best practices.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import black, red, blue, green
from reportlab.lib.units import mm, inch

def debug_coordinate_system():
    """Debug ReportLab coordinate system and positioning."""
    
    print("Debugging ReportLab coordinate system...")
    
    output_path = "output/coordinate_debug.pdf"
    os.makedirs("output", exist_ok=True)
    
    # Create canvas with A4 size
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    print(f"Page dimensions: {width} x {height} points")
    print(f"Page dimensions: {width/72:.2f} x {height/72:.2f} inches")
    
    # Draw coordinate grid to understand the system
    c.setStrokeColor(blue)
    c.setLineWidth(0.5)
    
    # Vertical lines every 50 points
    for x in range(0, int(width), 50):
        c.line(x, 0, x, height)
        if x % 100 == 0:  # Label every 100 points
            c.setFont("Helvetica", 8)
            c.setFillColor(blue)
            c.drawString(x + 2, 10, f"{x}")
    
    # Horizontal lines every 50 points
    for y in range(0, int(height), 50):
        c.line(0, y, width, y)
        if y % 100 == 0:  # Label every 100 points
            c.setFont("Helvetica", 8)
            c.setFillColor(blue)
            c.drawString(10, y + 2, f"{y}")
    
    # Mark the origin (0,0) - bottom left
    c.setFillColor(red)
    c.setStrokeColor(red)
    c.setLineWidth(2)
    c.circle(0, 0, 10, fill=1)
    c.setFont("Helvetica-Bold", 12)
    c.drawString(15, 5, "ORIGIN (0,0)")
    
    # Mark the top-left corner for reference
    c.setFillColor(green)
    c.setStrokeColor(green)
    c.circle(0, height, 10, fill=1)
    c.drawString(15, height - 15, f"TOP-LEFT (0,{height:.0f})")
    
    # Test text positioning at various Y coordinates
    test_y_positions = [100, 200, 300, 400, 500, 600, 700]
    
    c.setFillColor(black)
    c.setFont("Helvetica", 10)
    
    for i, y_pos in enumerate(test_y_positions):
        # Draw text
        text = f"Text at Y={y_pos}"
        c.drawString(100, y_pos, text)
        
        # Draw a reference line at the same Y position
        c.setStrokeColor(red)
        c.setLineWidth(1)
        c.line(90, y_pos, 300, y_pos)
        
        # Add a form field at the same Y position
        c.acroForm.textfield(
            name=f"field_{i}",
            x=320,
            y=y_pos - 5,  # Offset to see the difference
            width=150,
            height=15,
            value=f"Field {i}",
            forceBorder=True,
            fontName="Helvetica",
            fontSize=9
        )
        
        # Draw another reference line for the form field
        c.setStrokeColor(blue)
        c.line(310, y_pos - 5, 480, y_pos - 5)
    
    # Test the specific issue: text label vs form field alignment
    test_section_y = 750
    
    c.setFont("Helvetica-Bold", 14)
    c.setFillColor(black)
    c.drawString(50, test_section_y, "ALIGNMENT TEST SECTION")
    
    # Test case 1: Label above field (current approach)
    label_y = test_section_y - 50
    field_y = test_section_y - 70
    
    c.setFont("Helvetica", 8)
    c.drawString(50, label_y, "Label above field:")
    
    c.acroForm.textfield(
        name="test_above",
        x=50,
        y=field_y,
        width=200,
        height=15,
        value="Field below label",
        forceBorder=True,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Draw reference lines
    c.setStrokeColor(red)
    c.line(40, label_y, 260, label_y)
    c.setStrokeColor(blue)
    c.line(40, field_y, 260, field_y)
    
    # Test case 2: Label and field at same baseline
    baseline_y = test_section_y - 120
    
    c.setFont("Helvetica", 8)
    c.drawString(50, baseline_y, "Label at baseline:")
    
    c.acroForm.textfield(
        name="test_baseline",
        x=150,
        y=baseline_y - 7,  # Adjust for form field baseline
        width=150,
        height=15,
        value="Field at baseline",
        forceBorder=True,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Draw reference line
    c.setStrokeColor(green)
    c.line(40, baseline_y, 310, baseline_y)
    
    # Test case 3: Understanding form field coordinate system
    form_test_y = test_section_y - 180
    
    c.setFont("Helvetica", 8)
    c.drawString(50, form_test_y, "Form field coordinate test:")
    
    # Create form field and mark its corners
    field_x, field_y, field_w, field_h = 50, form_test_y - 30, 200, 20
    
    c.acroForm.textfield(
        name="test_corners",
        x=field_x,
        y=field_y,
        width=field_w,
        height=field_h,
        value="Corner test field",
        forceBorder=True,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Mark the corners of the form field
    c.setStrokeColor(red)
    c.setFillColor(red)
    # Bottom-left corner (x, y)
    c.circle(field_x, field_y, 3, fill=1)
    c.drawString(field_x + 5, field_y - 10, f"({field_x},{field_y})")
    
    # Top-right corner (x+w, y+h)
    c.circle(field_x + field_w, field_y + field_h, 3, fill=1)
    c.drawString(field_x + field_w - 50, field_y + field_h + 5, f"({field_x + field_w},{field_y + field_h})")
    
    # Add measurements and notes
    c.setFont("Helvetica", 8)
    c.setFillColor(black)
    notes_y = 50
    notes = [
        "COORDINATE SYSTEM ANALYSIS:",
        f"• Page size: {width:.0f} x {height:.0f} points",
        "• Origin (0,0) is at BOTTOM-LEFT corner",
        "• X increases to the RIGHT",
        "• Y increases UPWARD",
        "• Text drawString(x,y) positions text baseline at y",
        "• Form field textfield(x,y,w,h) positions bottom-left corner at (x,y)",
        "• Form field height extends UPWARD from y coordinate",
        "",
        "ALIGNMENT ISSUE ANALYSIS:",
        "• Text baseline and form field bottom are at different visual levels",
        "• Need to adjust either text Y or form field Y for proper alignment",
        "• Form field Y should be text Y minus (field_height - text_height)/2"
    ]
    
    for i, note in enumerate(notes):
        c.drawString(50, notes_y + (len(notes) - i - 1) * 12, note)
    
    c.save()
    print(f"Coordinate debug PDF generated: {output_path}")
    return output_path

def test_proper_alignment():
    """Test proper alignment based on coordinate system understanding."""
    
    print("Testing proper alignment...")
    
    output_path = "output/proper_alignment_test.pdf"
    
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "PROPER ALIGNMENT TEST")
    
    # Test proper alignment calculation
    section_y = height - 100
    
    # Method 1: Calculate form field Y to align with text baseline
    text_y = section_y
    text_height = 8  # Font size
    field_height = 15
    
    # For visual alignment, form field bottom should be slightly below text baseline
    field_y = text_y - (field_height - text_height) / 2 - 2
    
    c.setFont("Helvetica", 8)
    c.drawString(50, text_y, "Method 1 - Calculated alignment:")
    
    c.acroForm.textfield(
        name="method1_test",
        x=200,
        y=field_y,
        width=200,
        height=field_height,
        value="Properly aligned field",
        forceBorder=True,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Draw reference line
    c.setStrokeColor(green)
    c.line(45, text_y, 410, text_y)
    
    # Method 2: Use consistent offset
    section_y -= 50
    label_offset = 14  # Standard offset from research
    
    field_y2 = section_y
    label_y2 = field_y2 + label_offset
    
    c.setFont("Helvetica", 8)
    c.drawString(50, label_y2, "Method 2 - Label above with offset:")
    
    c.acroForm.textfield(
        name="method2_test",
        x=50,
        y=field_y2,
        width=200,
        height=15,
        value="Field with label above",
        forceBorder=True,
        fontName="Helvetica",
        fontSize=9
    )
    
    # Draw reference lines
    c.setStrokeColor(blue)
    c.line(45, label_y2, 260, label_y2)
    c.setStrokeColor(red)
    c.line(45, field_y2, 260, field_y2)
    
    # Method 3: Professional form layout
    section_y -= 80
    
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, section_y, "Method 3 - Professional Layout:")
    
    # Create a series of properly aligned fields
    fields_data = [
        ("Company name:", "Ringerike Landskap AS"),
        ("Organization number:", "***********"),
        ("Address:", "Hovedgata 123, 3500 Hønefoss")
    ]
    
    current_y = section_y - 30
    
    for label, value in fields_data:
        # Label positioned above field with proper spacing
        label_y = current_y + 14
        field_y = current_y
        
        c.setFont("Helvetica", 8)
        c.drawString(50, label_y, label)
        
        c.acroForm.textfield(
            name=f"professional_{label.replace(':', '').replace(' ', '_').lower()}",
            x=50,
            y=field_y,
            width=300,
            height=15,
            value=value,
            forceBorder=False,  # Use underline instead
            fontName="Helvetica",
            fontSize=9
        )
        
        # Add underline
        c.setStrokeColor(black)
        c.setLineWidth(0.5)
        c.line(50, field_y, 350, field_y)
        
        current_y -= 40  # Space between fields
    
    c.save()
    print(f"Proper alignment test PDF generated: {output_path}")
    return output_path

def main():
    """Main function to debug ReportLab coordinates."""
    
    print("ReportLab Coordinate System Debug")
    print("=" * 50)
    
    try:
        # Debug coordinate system
        print("\n1. Debugging coordinate system...")
        debug_path = debug_coordinate_system()
        
        # Test proper alignment
        print("\n2. Testing proper alignment...")
        alignment_path = test_proper_alignment()
        
        print("\n" + "=" * 50)
        print("SUCCESS: Debug PDFs generated!")
        print(f"Coordinate debug: {debug_path}")
        print(f"Alignment test: {alignment_path}")
        
        print("\nKey findings from research:")
        print("• ReportLab uses bottom-left origin (0,0)")
        print("• Text drawString() positions at baseline")
        print("• Form fields position at bottom-left corner")
        print("• Need to adjust Y coordinates for visual alignment")
        print("• Professional forms use label above field with 14pt offset")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
